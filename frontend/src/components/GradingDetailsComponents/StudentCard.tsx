// components/StudentCard/index.tsx
import React, { useMemo } from 'react';
import { AnswerSheetData, AnswerSheetResult } from '../../types/gradingTypes';
import { EVALUATION_CONFIG, CONSTANTS } from '../../config/evaluationConfig';
import { useSSEGradingProgress } from '../../hooks/useSSEGradingProgress';
import { StudentAvatar } from './StudentAvatar';
import { StudentInfo } from './StudentInfo';
import { GradingProgress } from './GradingProgress';
import { ScoreDisplay } from './ScoreDisplay';
import { ActionButton } from './ActionButton';
import { RefundButton } from './RefundButton';
import { calculatePercentage } from '../../utils/gradingUtils';


interface StudentCardProps {
    sheet: AnswerSheetData;
    onViewResults: (result: AnswerSheetResult) => void;
    formatResults: (sheet: AnswerSheetData) => AnswerSheetResult | null;
    onRefreshData?: () => void; // Optional callback to refresh submission data
    submissionId?: string; // For refund functionality
}

export const StudentCard: React.FC<StudentCardProps> = ({
    sheet,
    onViewResults,
    formatResults,
    onRefreshData,
    submissionId
}) => {
    // Parse evaluation data
    const parsedEvaluation = useMemo(() => {
        return EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(sheet.evaluationResult);
    }, [sheet.evaluationResult]);

    // Calculate scores
    const scores = useMemo(() => {
        if (!parsedEvaluation) return null;

        const totalMarks = parsedEvaluation.total_marks;
        const maxMarks = parsedEvaluation.maximum_possible_marks;
        let percentage = parsedEvaluation.percentage_score;

        if ((!percentage || percentage === 0) && totalMarks !== undefined && maxMarks !== undefined) {
            percentage = calculatePercentage(totalMarks, maxMarks);
        }

        return { totalMarks, maxMarks, percentage };
    }, [parsedEvaluation]);

    // SSE Progress tracking
    const sseState = useSSEGradingProgress({
        sheetId: sheet.id,
        hasEvaluationResult: !!sheet.evaluationResult,
        initialStatus: sheet.status
    });

    // Handle view results action
    const handleViewResults = () => {
        // If we have evaluation results, proceed normally
        if (sheet.evaluationResult) {
            const result = formatResults(sheet);
            if (result) onViewResults(result);
        } else if (sseState.status === 'completed' && onRefreshData) {
            // If grading just completed but we don't have evaluation results yet,
            // refresh the data to get the latest results
            onRefreshData();
        }
    };



    // Determine if actions should be shown
    // Show actions when either:
    // 1. We have evaluation results (already graded)
    // 2. SSE indicates grading is completed (just finished grading)
    const showActions = parsedEvaluation || sseState.status === 'completed';

    // Determine if refund button should be shown and if it's already refunded
    // Show refund button for sheets with 'error' status from database, regardless of SSE state
    const showRefundButton = sheet.status === 'error';
    const isSheetRefunded = sheet.refundStatus?.isRefunded || false;
    return (
        <div className="flex items-center gap-4 p-3 sm:p-4 border border-border dark:border-border rounded-lg hover:shadow-sm dark:hover:shadow-lg hover:border-primary/20 dark:hover:border-primary/30 transition-all duration-200">
            {/* Left: Avatar and Student Info */}
            <div className="flex items-center gap-3 flex-shrink-0">
                <StudentAvatar
                    name={sheet.studentName}
                    percentage={scores?.percentage}
                />
                <StudentInfo
                    name={sheet.studentName}
                    rollNumber={sheet.rollNumber}
                />
            </div>

            {/* Center: Progress Status */}
            <div className="flex-1 flex justify-center">
                {!showActions && (
                    <GradingProgress
                        progress={sseState.progress}
                        status={sseState.status}
                        isConnected={sseState.isConnected}
                        error={sseState.error}
                    />
                )}
            </div>

            {/* Right: Score and Action Buttons */}
            <div className="flex flex-col items-end gap-2 flex-shrink-0">
                {/* Score Display */}
                {parsedEvaluation && (
                    <ScoreDisplay
                        scores={scores}
                        hasError={sseState.status === 'error'}
                        isCompleted={sseState.status === 'completed'}
                    />
                )}

                {/* Action Buttons Container */}
                <div className="flex flex-col gap-2">
                    {/* View Details Button */}
                    {showActions && (
                        <ActionButton
                            onClick={handleViewResults}
                            label={sheet.evaluationResult && sseState.status === 'completed' && sheet.status === 'completed' ? "View Details" : "Refresh & View"}
                            shortLabel={sheet.evaluationResult ? "Details" : "Refresh"}
                        />
                    )}

                    {/* Refund Button for Failed Sheets */}
                    {showRefundButton && submissionId && (
                        <RefundButton
                            submissionId={submissionId}
                            sheetId={sheet.id}
                            onRefundSuccess={onRefreshData}
                            isRefunded={isSheetRefunded}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};
